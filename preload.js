window.exports = {
  // 主功能：打开图片生成器界面
  "img_generator": {
    mode: "none",
    args: {
      enter: () => {
        // 直接打开主界面
        window.utools.hideMainWindow();
        window.utools.redirect([{
          code: "img_generator",
          type: "over"
        }]);
      }
    }
  },

  // 尺寸匹配：例如输入 "800x600" 直接生成该尺寸图片
  "img_size_generator": {
    mode: "none",
    args: {
      enter: (action) => {
        const match = action.payload.match(/^(\d+)[x×*](\d+)$/);
        if (match) {
          const width = parseInt(match[1]);
          const height = parseInt(match[2]);

          // 设置默认参数并打开界面
          window.utools.hideMainWindow();
          window.utools.redirect([{
            code: "img_generator",
            type: "over"
          }]);

          // 通过localStorage传递参数
          localStorage.setItem('img_preset', JSON.stringify({
            width: width,
            height: height,
            type: 'solid'
          }));
        }
      }
    }
  },

  // 颜色匹配：例如输入 "#FF0000" 直接生成红色图片
  "img_color_generator": {
    mode: "none",
    args: {
      enter: (action) => {
        const color = action.payload;

        // 设置默认参数并打开界面
        window.utools.hideMainWindow();
        window.utools.redirect([{
          code: "img_generator",
          type: "over"
        }]);

        // 通过localStorage传递参数
        localStorage.setItem('img_preset', JSON.stringify({
          width: 800,
          height: 600,
          type: 'solid',
          color: color
        }));
      }
    }
  }
};
