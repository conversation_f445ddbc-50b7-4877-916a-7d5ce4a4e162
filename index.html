<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            padding: 24px;
            width: 100%;
            max-width: 400px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 24px;
        }
        
        .header h1 {
            color: #1f2937;
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .header p {
            color: #6b7280;
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #374151;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }
        
        .color-input {
            height: 40px;
            padding: 4px;
        }
        
        .button-group {
            display: flex;
            gap: 12px;
            margin-top: 24px;
        }
        
        .btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #4f46e5;
            color: white;
        }
        
        .btn-primary:hover {
            background: #4338ca;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .hidden {
            display: none;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 图片生成器</h1>
            <p>生成图片并复制到剪贴板</p>
        </div>
        
        <div id="main-menu">
            <div class="form-group">
                <label>选择生成类型：</label>
                <select id="imageType">
                    <option value="solid">纯色图片</option>
                    <option value="gradient">渐变图片</option>
                    <option value="transparent">透明图片</option>
                </select>
            </div>
            
            <div class="form-group">
                <label>宽度 (px):</label>
                <input type="number" id="width" value="800" min="1" max="10000">
            </div>
            
            <div class="form-group">
                <label>高度 (px):</label>
                <input type="number" id="height" value="600" min="1" max="10000">
            </div>

            <div class="form-group">
                <label>图片文案:</label>
                <input type="text" id="textOverlay" placeholder="留空则显示尺寸信息 (如: 800x600)">
            </div>

            <div class="form-group">
                <label>文字大小:</label>
                <input type="number" id="fontSize" value="24" min="8" max="200">
            </div>

            <div class="form-group">
                <label>文字颜色:</label>
                <input type="color" id="textColor" value="#FFFFFF" class="color-input">
            </div>

            <div id="solid-options">
                <div class="form-group">
                    <label>颜色:</label>
                    <input type="color" id="color" value="#4F46E5" class="color-input">
                </div>
            </div>
            
            <div id="gradient-options" class="hidden">
                <div class="form-group">
                    <label>起始颜色:</label>
                    <input type="color" id="startColor" value="#FF6B6B" class="color-input">
                </div>
                <div class="form-group">
                    <label>结束颜色:</label>
                    <input type="color" id="endColor" value="#4F46E5" class="color-input">
                </div>
                <div class="form-group">
                    <label>渐变方向:</label>
                    <select id="direction">
                        <option value="horizontal">水平</option>
                        <option value="vertical">垂直</option>
                        <option value="diagonal">对角线</option>
                        <option value="radial">径向</option>
                    </select>
                </div>
            </div>
            
            <div class="button-group">
                <button class="btn btn-primary" onclick="generateImage()">生成并复制</button>
                <button class="btn btn-secondary" onclick="resetForm()">重置</button>
            </div>

            <div style="margin-top: 12px; font-size: 12px; color: #6b7280; text-align: center;">
                <span id="clipboard-status">检测剪贴板功能中...</span>
            </div>
        </div>
    </div>

    <script>
        // 切换图片类型时显示/隐藏相关选项
        document.getElementById('imageType').addEventListener('change', function() {
            const type = this.value;
            const solidOptions = document.getElementById('solid-options');
            const gradientOptions = document.getElementById('gradient-options');
            
            solidOptions.classList.toggle('hidden', type !== 'solid');
            gradientOptions.classList.toggle('hidden', type !== 'gradient');
        });

        // 生成图片
        function generateImage() {
            const type = document.getElementById('imageType').value;
            const width = parseInt(document.getElementById('width').value);
            const height = parseInt(document.getElementById('height').value);
            
            if (!width || !height || width <= 0 || height <= 0) {
                alert('请输入有效的宽度和高度');
                return;
            }
            
            const canvas = document.createElement('canvas');
            canvas.width = width;
            canvas.height = height;
            const ctx = canvas.getContext('2d');
            
            let filename = '';
            
            switch(type) {
                case 'solid':
                    const color = document.getElementById('color').value;
                    ctx.fillStyle = color;
                    ctx.fillRect(0, 0, width, height);
                    filename = `solid_${width}x${height}_${color.replace('#', '')}.png`;
                    break;
                    
                case 'gradient':
                    const startColor = document.getElementById('startColor').value;
                    const endColor = document.getElementById('endColor').value;
                    const direction = document.getElementById('direction').value;
                    
                    let gradient;
                    switch(direction) {
                        case 'horizontal':
                            gradient = ctx.createLinearGradient(0, 0, width, 0);
                            break;
                        case 'vertical':
                            gradient = ctx.createLinearGradient(0, 0, 0, height);
                            break;
                        case 'diagonal':
                            gradient = ctx.createLinearGradient(0, 0, width, height);
                            break;
                        case 'radial':
                            gradient = ctx.createRadialGradient(width/2, height/2, 0, width/2, height/2, Math.max(width, height)/2);
                            break;
                    }
                    
                    gradient.addColorStop(0, startColor);
                    gradient.addColorStop(1, endColor);
                    ctx.fillStyle = gradient;
                    ctx.fillRect(0, 0, width, height);
                    filename = `gradient_${width}x${height}_${direction}.png`;
                    break;
                    
                case 'transparent':
                    ctx.clearRect(0, 0, width, height);
                    filename = `transparent_${width}x${height}.png`;
                    break;
            }

            // 添加文字
            const textOverlay = document.getElementById('textOverlay').value.trim();
            const fontSize = parseInt(document.getElementById('fontSize').value);
            const textColor = document.getElementById('textColor').value;

            // 如果没有输入文案，使用默认的尺寸信息
            const displayText = textOverlay || `${width}x${height}`;

            if (displayText) {
                ctx.font = `${fontSize}px Arial, sans-serif`;
                ctx.fillStyle = textColor;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';

                // 添加文字阴影以提高可读性
                ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
                ctx.shadowBlur = 2;
                ctx.shadowOffsetX = 1;
                ctx.shadowOffsetY = 1;

                // 在画布中心绘制文字
                ctx.fillText(displayText, width / 2, height / 2);

                // 重置阴影设置
                ctx.shadowColor = 'transparent';
                ctx.shadowBlur = 0;
                ctx.shadowOffsetX = 0;
                ctx.shadowOffsetY = 0;
            }

            // 复制图片到剪贴板（带大小控制）
            compressAndCopyImage(canvas, filename);
        }

        // 压缩并复制图片
        async function compressAndCopyImage(canvas, filename) {
            const maxSizeKB = 200; // 最大200KB
            const maxSizeBytes = maxSizeKB * 1024;

            let quality = 0.9; // 初始质量
            let blob;

            // 尝试不同的压缩质量直到文件大小满足要求
            do {
                blob = await new Promise(resolve => {
                    canvas.toBlob(resolve, 'image/jpeg', quality);
                });

                if (blob.size <= maxSizeBytes || quality <= 0.1) {
                    break;
                }

                quality -= 0.1; // 降低质量
            } while (quality > 0.1);

            // 如果JPEG压缩后仍然太大，尝试PNG
            if (blob.size > maxSizeBytes) {
                blob = await new Promise(resolve => {
                    canvas.toBlob(resolve, 'image/png');
                });
            }

            // 显示文件大小信息
            const sizeKB = Math.round(blob.size / 1024);
            const sizeInfo = ` (${sizeKB}KB)`;

            // 尝试复制到剪贴板
            let copySuccess = false;

            // 检查是否支持现代剪贴板API
            if (navigator.clipboard && navigator.clipboard.write && window.ClipboardItem) {
                try {
                    // 确保在用户交互的上下文中执行
                    const clipboardItem = new ClipboardItem({
                        [blob.type]: blob
                    });

                    await navigator.clipboard.write([clipboardItem]);
                    copySuccess = true;
                    showNotification(`图片已复制到剪贴板！${sizeInfo}`, 'success');

                } catch (error) {
                    console.error('剪贴板API失败:', error.message);
                    // 如果是权限问题，提示用户
                    if (error.name === 'NotAllowedError') {
                        showNotification('请允许访问剪贴板权限', 'warning');
                    }
                }
            }

            // 如果所有复制方法都失败，使用下载功能
            if (!copySuccess) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                showNotification(`剪贴板功能不可用，已下载图片。${sizeInfo}`, 'warning');
            }
        }

        // 重置表单
        function resetForm() {
            document.getElementById('width').value = '800';
            document.getElementById('height').value = '600';
            document.getElementById('textOverlay').value = '';
            document.getElementById('fontSize').value = '24';
            document.getElementById('textColor').value = '#FFFFFF';
            document.getElementById('color').value = '#4F46E5';
            document.getElementById('startColor').value = '#FF6B6B';
            document.getElementById('endColor').value = '#4F46E5';
            document.getElementById('direction').value = 'horizontal';
            document.getElementById('imageType').value = 'solid';

            // 触发类型变更事件
            document.getElementById('imageType').dispatchEvent(new Event('change'));
        }

        // 加载预设参数
        function loadPresetParams() {
            const preset = localStorage.getItem('img_preset');
            if (preset) {
                try {
                    const params = JSON.parse(preset);

                    // 设置文字输入
                    if (params.textOverlay) {
                        document.getElementById('textOverlay').value = params.textOverlay;
                    }
                    if (params.fontSize) {
                        document.getElementById('fontSize').value = params.fontSize;
                    }
                    if (params.textColor) {
                        document.getElementById('textColor').value = params.textColor;
                    }

                    // 设置尺寸
                    if (params.width) {
                        document.getElementById('width').value = params.width;
                    }
                    if (params.height) {
                        document.getElementById('height').value = params.height;
                    }

                    // 设置类型
                    if (params.type) {
                        document.getElementById('imageType').value = params.type;
                        document.getElementById('imageType').dispatchEvent(new Event('change'));
                    }

                    // 设置颜色
                    if (params.color) {
                        document.getElementById('color').value = params.color;
                    }

                    // 清除预设参数
                    localStorage.removeItem('img_preset');

                    // 显示提示
                    showNotification('已自动填入参数', 'info');

                } catch (e) {
                    console.error('解析预设参数失败:', e);
                    localStorage.removeItem('img_preset');
                }
            }
        }

        // 显示通知消息
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');

            let backgroundColor;
            switch(type) {
                case 'success':
                    backgroundColor = '#10B981'; // 绿色
                    break;
                case 'warning':
                    backgroundColor = '#F59E0B'; // 橙色
                    break;
                case 'error':
                    backgroundColor = '#EF4444'; // 红色
                    break;
                default:
                    backgroundColor = '#4F46E5'; // 蓝色
            }

            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${backgroundColor};
                color: white;
                padding: 12px 16px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                z-index: 1000;
                animation: slideIn 0.3s ease-out;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                max-width: 300px;
                word-wrap: break-word;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            // 3秒后移除提示
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.animation = 'slideIn 0.3s ease-out reverse';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }
            }, 3000);
        }

        // 检测剪贴板功能
        async function checkClipboardSupport() {
            const statusElement = document.getElementById('clipboard-status');

            // 基本API检查
            if (!navigator.clipboard || !navigator.clipboard.write || !window.ClipboardItem) {
                statusElement.textContent = '⚠️ 浏览器不支持剪贴板API，将自动下载图片';
                statusElement.style.color = '#F59E0B';
                return;
            }

            // 安全上下文检查
            if (!window.isSecureContext) {
                statusElement.textContent = '⚠️ 需要HTTPS环境才能使用剪贴板功能，将自动下载图片';
                statusElement.style.color = '#F59E0B';
                return;
            }

            // 权限检查
            try {
                const permission = await navigator.permissions.query({name: 'clipboard-write'});
                if (permission.state === 'granted') {
                    statusElement.textContent = '✅ 剪贴板功能可用';
                    statusElement.style.color = '#10B981';
                } else if (permission.state === 'prompt') {
                    statusElement.textContent = '🔄 剪贴板功能可用 (首次使用需要授权)';
                    statusElement.style.color = '#4F46E5';
                } else {
                    statusElement.textContent = '❌ 剪贴板权限被拒绝，将自动下载图片';
                    statusElement.style.color = '#EF4444';
                }
            } catch (error) {
                // 如果权限API不可用，假设功能可用
                statusElement.textContent = '✅ 剪贴板功能可用';
                statusElement.style.color = '#10B981';
            }
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadPresetParams();
            checkClipboardSupport();
        });
    </script>
</body>
</html>
